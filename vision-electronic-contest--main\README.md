# vision-electronic-contest-
本项目旨在为准备电子设计竞赛以及其他竞赛活动的大学生提供若干视觉成品，快速提升开发水平。本项目基于opencv，在树莓派、jetson系列或者是支持opencv的板子上使用。涵盖了颜色区域提取、几何图形识别、特定logo匹配、激光点检测、雷达与视觉融合等功能，同时对这些功能的组合搭配进行了优化和封装，使孤立功能不再是单元函数，而是相互有机结合，协同作战。

## 项目特色
- 实用：涵盖颜色区域提取、几何图形识别、特定logo匹配、激光点检测、雷达与视觉融合等功能，同时对这些功能的组合搭配进行了优化和封装，使孤立功能不再是单元函数，而是相互有机结合，协同作战。
- 易懂：大量注释，适合学习与二次开发
- 开源：欢迎电赛视觉方向的同学共同完善和交流。

## 适用对象
- 电赛视觉组选手
- 有志于学习图像处理/视觉算法的大学生
- 相关项目开发者

## 快速开始
1. 克隆仓库  
   ```bash
   git clone https://github.com/ziqi-ning/vision-electronic-contest.git
   ```
2. 安装依赖
   - Python >= 3.6
   - OpenCV
   - numpy
   - pyzbar
   - apriltag
   - ROS1（可选，雷达相关用到）

3. 运行案例  
   参考各类 `调用案例` 文件，可以直接运行测试。

## 贡献方式
- 欢迎 PR（代码优化、功能补充、文档完善等）
- 也可提交 Issue 交流使用问题或需求
- 欢迎加注释、补案例，让更多人受益

## 联系与交流
- GitHub Issue
- 电赛交流群（可在 Issue 留言，大家自发组群）

---

**本项目只为帮助电赛视觉工作者共同提升水平。祝大家电赛顺利，不留遗憾！**

